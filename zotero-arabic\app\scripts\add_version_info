#!/usr/bin/env python3
"""
Update a builds manifest with info on a given build
"""
import argparse
import os
import sys
import shutil
import json
import traceback

MAJOR = None

parser = argparse.ArgumentParser(
    description='Update a builds manifest with info on a given build',
    formatter_class=argparse.ArgumentDefaultsHelpFormatter)

parser.add_argument('-f', '--file', required=True, help="path to updates.json")
parser.add_argument('-v', '--version', required=True, help='version number of build')
parser.add_argument('-b', '--build_id', required=True, help="build ID ('20160801142343')")

args = parser.parse_args()

def main():
    try:
        file = args.file
        version = args.version
        short_version = version[0:3]
        
        # Back up JSON file
        shutil.copy2(file, file + '.bak')
        
        # Read in existing file
        with open(file) as f:
            updates = json.loads(f.read())
        
        updates.append({
            'version': version,
            'buildID': args.build_id,
            'detailsURL': f'https://www.zotero.org/support/{short_version}_changelog',
            'major': MAJOR
        })
        
        # Keep last 5 entries
        updates = updates[-5:]
        
        # Write new file
        updates = json.dumps(updates, indent=2)
        with open(file, 'w') as f:
            f.write(updates + "\n")
        
        print(updates)
        
        return 0
    
    except Exception as err:
        sys.stderr.write("\n" + traceback.format_exc())
        return 1

if __name__ == '__main__':
    sys.exit(main())
