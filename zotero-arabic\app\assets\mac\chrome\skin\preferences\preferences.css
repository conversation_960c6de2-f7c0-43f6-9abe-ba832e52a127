/*
# -*- Mode: Java; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*-
# ***** BEGIN LICENSE BLOCK *****
# Version: MPL 1.1/GPL 2.0/LGPL 2.1
#
# The contents of this file are subject to the Mozilla Public License Version
# 1.1 (the "License"); you may not use this file except in compliance with
# the License. You may obtain a copy of the License at
# http://www.mozilla.org/MPL/
#
# Software distributed under the License is distributed on an "AS IS" basis,
# WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License
# for the specific language governing rights and limitations under the
# License.
#
# The Original Code is the Firefox Preferences System.
#
# The Initial Developer of the Original Code is
# <PERSON>.
# Portions created by the Initial Developer are Copyright (C) 2005
# the Initial Developer. All Rights Reserved.
#
# Contributor(s):
#   <PERSON> <<EMAIL>>
#   <PERSON> <<EMAIL>>
#
# Alternatively, the contents of this file may be used under the terms of
# either the GNU General Public License Version 2 or later (the "GPL"), or
# the GNU Lesser General Public License Version 2.1 or later (the "LGPL"),
# in which case the provisions of the GPL or the LGPL are applicable instead
# of those above. If you wish to allow use of your version of this file only
# under the terms of either the GPL or the LGPL, and not to allow others to
# use your version of this file under the terms of the MPL, indicate your
# decision by deleting the provisions above and replace them with the notice
# and other provisions required by the GPL or the LGPL. If you do not delete
# the provisions above, a recipient may use your version of this file under
# the terms of any one of the MPL, the GPL or the LGPL.
#
# ***** END LICENSE BLOCK *****
*/

.windowDialog {
  padding: 12px;
  font: -moz-dialog;
}

.paneSelector {
  list-style-image: url("chrome://browser/skin/preferences/Options.png");
}

/* ----- GENERAL BUTTON ----- */

radio[pane=paneGeneral],
radio[pane=paneMain] {
  -moz-image-region: rect(0px, 32px, 32px, 0px);
} 

/* ----- TABS BUTTON ----- */

radio[pane=paneTabs] {
  -moz-image-region: rect(0px, 64px, 32px, 32px);
}

/* ----- CONTENT BUTTON ----- */

radio[pane=paneContent] {
  -moz-image-region: rect(0px, 96px, 32px, 64px);
}

/* ----- APPLICATIONS BUTTON ----- */

radio[pane=paneApplications] {
  -moz-image-region: rect(0px, 128px, 32px, 96px);
}

/* ----- PRIVACY BUTTON ----- */

radio[pane=panePrivacy] {
  -moz-image-region: rect(0px, 160px, 32px, 128px);
}

/* ----- SECURITY BUTTON ----- */

radio[pane=paneSecurity] {
  -moz-image-region: rect(0px, 192px, 32px, 160px);
}

/* ----- ADVANCED BUTTON ----- */

radio[pane=paneAdvanced] {
  -moz-image-region: rect(0px, 224px, 32px, 192px);
}

/* ----- SYNC BUTTON ----- */

radio[pane=paneSync] {
  list-style-image: url("chrome://browser/skin/preferences/Options-sync.png");
}


/* ----- APPLICATIONS PREFPANE ----- */
#BrowserPreferences[animated="true"] #handlersView {
  height: 25em;
}

#BrowserPreferences[animated="false"] #handlersView {
  -moz-box-flex: 1;
}

description {
  font: small-caption;
  font-weight: normal;
  line-height: 1.3em;
  margin-bottom: 4px !important;
}

prefpane .groupbox-body {
  -moz-appearance: none;
  padding: 8px 4px 4px 4px;
}

#paneTabs > groupbox {
  margin: 0;
}

#tabPrefsBox {
  margin: 12px 4px;
}

prefpane .groupbox-title {
  background: url("chrome://global/skin/50pct_transparent_grey.png") repeat-x bottom left;
  margin-bottom: 4px;
}

tabpanels {
  padding: 20px 7px 7px;
}

caption {
  -moz-padding-start: 5px;
  padding-top: 4px;
  padding-bottom: 2px;
}

#paneMain description,
#paneContent description,
#paneAdvanced description,
#paneSecurity description {
  font: -moz-dialog;
}

#paneContent {
  padding-top: 8px;
}

#paneContent row {
  padding: 2px 4px;
  -moz-box-align: center;
}

#popupPolicyRow,
#enableSoftwareInstallRow,
#enableImagesRow {
  margin-bottom: 4px !important;
  padding-bottom: 4px !important;
  border-bottom: 1px solid #ccc;
}

#browserUseCurrent,
#browserUseBookmark,
#browserUseBlank {
  margin-top: 10px;
}

#advancedPrefs {
  margin: 0 8px;
}

#privacyPrefs {
  padding: 0 4px;
}

#privacyPrefs > tabpanels {
  padding: 18px 10px 10px;
}

#OCSPDialogPane {
  font: message-box !important;
}

/**
 * Privacy Pane
 */

/* styles for the link elements copied from .text-link in global.css */
.inline-link {
  color: LinkText;
  text-decoration: underline;
}
  
.inline-link:not(:focus) {
  outline: 1px dotted transparent;
}

/**
 * Update Preferences
 */
#autoInstallOptions {
  -moz-margin-start: 20px;
}

.updateControls {
  -moz-margin-start: 10px;
}

/**
 * Clear Private Data
 */
#SanitizeDialogPane > groupbox {
  margin-top: 0;
}


/* ----- SYNC PANE ----- */

#syncDesc {
  padding: 0 8em;
}

#accountCaptionImage {
  list-style-image: url("chrome://mozapps/skin/profile/profileicon.png");
}

#syncAddDeviceLabel {
  margin-top: 1em;
  margin-bottom: 1em;
}

#syncEnginesList {
  height: 10em;
}

