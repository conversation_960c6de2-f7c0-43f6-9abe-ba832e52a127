Accessible.tlb
AccessibleHandler.dll
IA2Marshal.dll
Microsoft.VC80.CRT.manifest
api-ms-win-core-console-l1-1-0.dll
api-ms-win-core-datetime-l1-1-0.dll
api-ms-win-core-debug-l1-1-0.dll
api-ms-win-core-errorhandling-l1-1-0.dll
api-ms-win-core-file-l1-1-0.dll
api-ms-win-core-handle-l1-1-0.dll
api-ms-win-core-heap-l1-1-0.dll
api-ms-win-core-interlocked-l1-1-0.dll
api-ms-win-core-libraryloader-l1-1-0.dll
api-ms-win-core-memory-l1-1-0.dll
api-ms-win-core-namedpipe-l1-1-0.dll
api-ms-win-core-processenvironment-l1-1-0.dll
api-ms-win-core-processthreads-l1-1-0.dll
api-ms-win-core-profile-l1-1-0.dll
api-ms-win-core-rtlsupport-l1-1-0.dll
api-ms-win-core-string-l1-1-0.dll
api-ms-win-core-synch-l1-1-0.dll
api-ms-win-core-sysinfo-l1-1-0.dll
api-ms-win-core-util-l1-1-0.dll
application.ini
chrome.manifest
chrome/*
components/*
defaults/*
deleted.txt
dictionaries/*
extensions/*
firefox.*
fonts/EmojiOneMozilla.ttf
freebl3.chk
gkmedias.dll
install.rdf
minidump-analyzer.exe
mozcrt19.dll
mozutils.dll
msvcp80.dll
msvcp120.dll
msvcr80.dll
msvcr120.dll
nssdbm3.chk
nssdbm3.dll
pdfinfo.exe
pdftotext.exe
pingsender.exe
plugin-hang-ui.exe
poppler-data/*
resource/*
softokn3.chk
styles.zip
test/*
translators.index
translators.zip
xulrunner/*
zotero.jar
