#!/bin/bash
set -uo pipefail

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
APP_ROOT_DIR="$(dirname "$SCRIPT_DIR")"
ROOT_DIR="$(dirname "$APP_ROOT_DIR")"
. "$APP_ROOT_DIR/config.sh"
. "$SCRIPT_DIR/utils.sh"

cd "$ROOT_DIR"

platform=`get_current_platform`

FAIL_CMD='echo -e \033[31;1mFAIL\033[0m'
FAIL_CMD_INLINE='echo -n -e \033[31;1mFAIL\033[0m'
FAILED=0

hdr_start=`tput smul`
hdr_stop=`tput rmul`

echo "${hdr_start}Checking build requirements:${hdr_stop}"
echo

echo -n "Checking for Node.js: "
which node || { $FAIL_CMD_INLINE; FAILED=1; }
node_version=$(node -v | cut -c 2-)
echo -n "Checking Node version: $node_version"
if [ $(echo $node_version | cut -d '.' -f1 ) -ge "18" ]; then
	echo
else
	echo -n " -- must be 18 or later "
	{ $FAIL_CMD; FAILED=1;  }
fi

echo -n "Checking for Git LFS: "
which git-lfs || { $FAIL_CMD_INLINE; FAILED=1; }

echo "Checking for Git LFS checkouts: "
lfs_files=$(git lfs ls-files -n 2> /dev/null) || { $FAIL_CMD; FAILED=1; }
for lfs_file in $lfs_files; do
	echo -n "    - $lfs_file: "
	file_type=$(file -b $lfs_file)
	echo -n $file_type" "
	if [ -z "$(echo -n $file_type | grep XZ)" ]; then
		{ $FAIL_CMD_INLINE; FAILED=1; echo " -- run 'git lfs install; git lfs pull'"; }
	fi
	echo
done

echo -n "Checking for perl: "
which perl || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for python3: "
which python3 || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for curl: "
which curl || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for wget: "
which wget || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for zip: "
which zip || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for unzip: "
which unzip || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for xz: "
which xz || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for awk: "
which awk || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for rsync: "
which rsync || { $FAIL_CMD; FAILED=1; }

if [ $platform = "w" ]; then
	echo -n "Checking for 7z: "
	which 7z || { $FAIL_CMD; FAILED=1; }
	
	echo -n "Checking for rcedit: "
	which rcedit || { $FAIL_CMD; FAILED=1; echo " -- Install with fetch_rcedit"; }
fi

if [ $platform = "w" ]; then
	echo
	echo "${hdr_start}Checking Windows packaging requirements:${hdr_stop}"
	echo
	
	echo -n "Checking for upx: "
	which upx || { $FAIL_CMD; FAILED=1; }

	echo -n "Checking for uuidgen: "
	which uuidgen || { $FAIL_CMD; FAILED=1; }
	
	echo -n "Checking for code-signing script: "
	if [ -x "$APP_ROOT_DIR/win/codesign" ]; then
		echo "$APP_ROOT_DIR/win/codesign"
	else
		$FAIL_CMD
		FAILED=1
	fi
	
	echo -n "Checking for Unicode NSIS: "
	if [ -x "`cygpath -u \"${NSIS_DIR}makensis.exe\"`" ]; then
		echo "`cygpath -u \"${NSIS_DIR}makensis.exe\"`"
	else
		$FAIL_CMD
		FAILED=1
	fi
	
	plugin_path=$(cd "$NSIS_DIR\\Plugins\\x86-unicode" && pwd)
	plugins="AppAssocReg ApplicationID InvokeShellVerb nsProcess ShellLink UAC"
	echo "Checking for NSIS plugins in $plugin_path"
	for i in $plugins; do
		echo -n "  $i.dll: "
		if [ -f "$plugin_path/$i.dll" ]; then
			echo OK
		else
			$FAIL_CMD
			FAILED=1
		fi
	done
fi

if [ $platform = "m" ]; then
	echo
	echo "${hdr_start}Checking Mac packaging requirements:${hdr_stop}"
	echo
	
	echo -n "Checking for codesign: "
	which /usr/bin/codesign || { $FAIL_CMD; FAILED=1; }
fi

echo
echo "${hdr_start}Checking distribution requirements:${hdr_stop}"
echo

echo -n "Checking for Mozilla ARchive (MAR) tool: "
which mar || { $FAIL_CMD; FAILED=1; echo "  -- Install with fetch_mar_tools"; }

echo -n "Checking for mbsdiff: "
which mbsdiff || { $FAIL_CMD; FAILED=1; echo "  -- Install with fetch_mar_tools"; }

echo -n "Checking for rsync: "
which rsync || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for sha512sum/shasum: "
which sha512sum 2>/dev/null || which shasum 2>/dev/null || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for AWS CLI: "
which aws || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for AWS S3 access: "
aws s3 ls $S3_BUCKET/$S3_DIST_PATH | sed 's/^[[:blank:]]*//' || { $FAIL_CMD; FAILED=1; }

echo -n "Checking for deploy host directory access: "
ssh $DEPLOY_HOST ls -d $DEPLOY_PATH || { $FAIL_CMD; FAILED=1; }


exit $FAILED
