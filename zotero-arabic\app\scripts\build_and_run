#!/bin/bash -e

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
APP_ROOT_DIR="$(dirname "$SCRIPT_DIR")"
ROOT_DIR="$(dirname $APP_ROOT_DIR)"

# Set ZOTERO_PROFILE environment variable to choose profile
profile_args=()
if [ -n "${ZOTERO_PROFILE:-}" ]; then
	profile_args=(-p "$ZOTERO_PROFILE")
fi

REBUILD=0
SKIP_BUNDLED_FILES=0
DEBUGGER=0
while getopts "rbd" opt; do
	case $opt in
		r)
			REBUILD=1
			;;
		
		b)
			SKIP_BUNDLED_FILES=1
			;;
		
		d)
			DEBUGGER=1
			;;
		
		\?)
			echo "Invalid option: -$OPTARG" >&2
			exit 1
			;;
	esac
done

# Remove options from $@
shift $((OPTIND-1))

if [ $REBUILD -eq 1 ]; then
	PARAMS=""
	if [ $DEBUGGER -eq 1 ]; then
		PARAMS="-t"
	fi
	
	# Check if build watch is running
	# If not, run now
	if ! ps u | grep js-build/build.js | grep -v grep > /dev/null; then
		echo "Running JS build process"
		echo
		cd $ROOT_DIR
		# TEMP: --openssl-legacy-provider avoids a build error in pdf.js
		NODE_OPTIONS=--openssl-legacy-provider npm run build
		echo
	fi
	
	"$SCRIPT_DIR/dir_build" -q $PARAMS
	
	if [ "`uname`" = "Darwin" ]; then
		# Sign the Word dylib so it works on Apple Silicon
		"$SCRIPT_DIR/codesign_local" "$APP_ROOT_DIR/staging/Zotero.app"
	fi
fi

PARAMS=""
if [ $SKIP_BUNDLED_FILES -eq 1 ]; then
	PARAMS="$PARAMS -ZoteroSkipBundledFiles"
fi
if [ $DEBUGGER -eq 1 ]; then
	PARAMS="$PARAMS -jsdebugger"
else
	PARAMS="$PARAMS -jsconsole"
fi

if [ "`uname`" = "Darwin" ]; then
	command="Zotero.app/Contents/MacOS/zotero"
elif [ "`uname`" = "Linux" ]; then
	command="Zotero_linux-x86_64/zotero"
elif [ "`uname -o 2> /dev/null`" = "Cygwin" ]; then
	command="Zotero_win-x64/zotero.exe"
else
	echo "Unknown platform" >&2
	exit 1
fi

"$APP_ROOT_DIR/staging/$command" "${profile_args[@]}" -ZoteroDebugText -purgecaches $PARAMS "$@"
