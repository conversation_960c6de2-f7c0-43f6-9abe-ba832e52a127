#!/bin/bash
set -euo pipefail

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
APP_ROOT_DIR="$(dirname "$SCRIPT_DIR")"
. "$APP_ROOT_DIR/config.sh"
cd "$APP_ROOT_DIR"

platform=""
while getopts ":p:" opt; do
	case $opt in
		p)
			platform="$OPTARG"
			;;
		\?)
			echo "Invalid option: -$OPTARG" >&2
			exit 1
			;;
		:)
			echo "Option -$OPTARG requires an argument." >&2
			exit 1
			;;
	esac
done


if [ $platform == "m" ]; then
		GECKO_VERSION="$GECKO_VERSION_MAC"
elif [ $platform == "w" ]; then
		GECKO_VERSION="$GECKO_VERSION_WIN"
elif [ $platform == "l" ]; then
		GECKO_VERSION="$GECKO_VERSION_LINUX"
else
		echo "Platform parameter incorrect. Usage: -p m(mac)/w(windows)/l(linux)"
		exit 1
fi

xulrunner_content=$(< "$SCRIPT_DIR/fetch_xulrunner")
xulrunner_content+=$(< "$SCRIPT_DIR/utils.sh")
xulrunner_content+=$(< "$APP_ROOT_DIR/assets/multilocale.txt")
xulrunner_gecko_hash=$(echo -n "$GECKO_VERSION - '$xulrunner_content'" | openssl dgst -sha256)

echo "$xulrunner_gecko_hash"


