%if 0
/*
# -*- Mode: Java; tab-width: 4; indent-tabs-mode: nil; c-basic-offset: 4 -*-
# ***** BEGIN LICENSE BLOCK *****
# Version: MPL 1.1/GPL 2.0/LGPL 2.1
#
# The contents of this file are subject to the Mozilla Public License Version
# 1.1 (the "License"); you may not use this file except in compliance with
# the License. You may obtain a copy of the License at
# http://www.mozilla.org/MPL/
#
# Software distributed under the License is distributed on an "AS IS" basis,
# WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License
# for the specific language governing rights and limitations under the
# License.
#
# The Original Code is the Firefox Preferences System.
#
# The Initial Developer of the Original Code is
# Ben <PERSON>ger.
# Portions created by the Initial Developer are Copyright (C) 2005
# the Initial Developer. All Rights Reserved.
#
# Contributor(s):
#   <PERSON> <<EMAIL>>
#
# Alternatively, the contents of this file may be used under the terms of
# either the GNU General Public License Version 2 or later (the "GPL"), or
# the GNU Lesser General Public License Version 2.1 or later (the "LGPL"),
# in which case the provisions of the GPL or the LGPL are applicable instead
# of those above. If you wish to allow use of your version of this file only
# under the terms of either the GPL or the LGPL, and not to allow others to
# use your version of this file under the terms of the MPL, indicate your
# decision by deleting the provisions above and replace them with the notice
# and other provisions required by the GPL or the LGPL. If you do not delete
# the provisions above, a recipient may use your version of this file under
# the terms of any one of the MPL, the GPL or the LGPL.
#
# ***** END LICENSE BLOCK *****
*/
%endif

/* Global Styles */
#BrowserPreferences radio[pane] {
  list-style-image: url("chrome://browser/skin/preferences/Options.png"); 
}

radio[pane=paneMain] {
  -moz-image-region: rect(0px, 32px,  32px, 0px)
}

radio[pane=paneTabs] {
  -moz-image-region: rect(0px, 64px, 32px, 32px)
}

radio[pane=paneContent] {
  -moz-image-region: rect(0px, 96px,  32px, 64px)
}

radio[pane=paneApplications] {
  -moz-image-region: rect(0px, 128px,  32px, 96px)
}

radio[pane=panePrivacy] {
  -moz-image-region: rect(0px, 160px,  32px, 128px)
}

radio[pane=paneSecurity] {
  -moz-image-region: rect(0px, 192px,  32px, 160px)
}

radio[pane=paneAdvanced] {
  -moz-image-region: rect(0px, 224px, 32px, 192px)
}

%ifdef MOZ_SERVICES_SYNC
radio[pane=paneSync] {
  list-style-image: url("chrome://browser/skin/preferences/Options-sync.png") !important;
}
%endif

/* Applications Pane */
#BrowserPreferences[animated="true"] #handlersView {
  height: 25em;
}

#BrowserPreferences[animated="false"] #handlersView {
  -moz-box-flex: 1;
}

/* Privacy Pane */

/* styles for the link elements copied from .text-link in global.css */
.inline-link {
  color: LinkText;
  text-decoration: underline;
}
  
.inline-link:not(:focus) {
  outline: 1px dotted transparent;
}

/* Modeless Window Dialogs */
.windowDialog,
.windowDialog prefpane {
  padding: 0px;
}

.contentPane {
  margin: 9px 8px 5px 8px;
}

.actionButtons {
  margin: 0px 3px 6px 3px !important;
}

/* Cookies Manager */
#cookiesChildren::-moz-tree-image(domainCol) {
  width: 16px;
  height: 16px;
  margin: 0px 2px;
  list-style-image: url("chrome://mozapps/skin/places/defaultFavicon.png");
}

#paneApplications {
  margin-left: 4px;
  margin-right: 4px; 
  padding-left: 0;
  padding-right: 0; 
}

#linksOpenInBox {
  margin-top: 5px;
}

#paneAdvanced {
  padding-bottom: 10px;
}
#advancedPrefs {
  margin-left: 0;
  margin-right: 0; 
}

#cookiesChildren::-moz-tree-image(domainCol, container) {
  list-style-image: url("moz-icon://stock/gtk-directory?size=menu");
}

#cookieInfoBox {
  border: 1px solid ThreeDShadow;
  border-radius: 0px;
  margin: 4px;
  padding: 0px;
}

/* bottom-most box containing a groupbox in a prefpane. Prevents the bottom
   of the groupbox from being cutoff */
.bottomBox {
  padding-bottom: 4px;
}

/**
 * Clear Private Data
 */
#SanitizeDialogPane > groupbox {
  margin-top: 0;
}

%ifdef MOZ_SERVICES_SYNC
/* Sync Pane */

#syncDesc {
  padding: 0 8em;
}

#accountCaptionImage {
  list-style-image: url("chrome://mozapps/skin/profile/profileicon.png");
}

#syncAddDeviceLabel {
  margin-top: 1em;
  margin-bottom: 1em;
}

#syncEnginesList {
  height: 10em;
}

%endif
